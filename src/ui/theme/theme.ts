import { createTheme } from "@shopify/restyle";

const palette = {
  midnightBlack: "#020817",
  background: "background",
  white: "#FFFFFF",
  black: "#000000",
  transparent: "transparent",
  disabled: "#0000099",

  iconGray: "#EDEDED",
  gray: "#e5e7ebcc",
  gray50: "#f9fafb",
  gray200: "#e5e7eb",
  gray500: "#6b7280",
  gray600: "#4b5563",
  gray900: "#111827",
  
  zinc: "#1E1E2D",
  zinc50: "#3D4045",
  zinc100: "#ADADAD",
  zinc200: "#A2A2A7",

  grayInput: "#E4E4E499",

  grayTabBar: "#F4F4F4",

  borderGray: "#DADADA",
  borderGrayTabBar: "#8B8B94",

  blue: "#0066FF",
  green: "#3ACA33",
  textTertiary: "#8E8E8E",
};
const theme = createTheme({
  colors: {
    text: palette.midnightBlack,
    textPrimary: palette.zinc,
    textSecondary: palette.textTertiary,
    border: palette.borderGray,
    ...palette,
  },
  spacing: {
    s2: 2,
    s4: 4,
    s8: 8,
    s10: 10,
    s12: 12,
    s14: 14,
    s16: 16,
    s20: 20,
    s24: 24,
    s32: 32,
    s36: 36,
    s40: 40,
    s42: 42,
    s48: 48,
    s56: 56,
    padding: 16,
  },
  textVariants: {
    defaults: {
      color: "text",
      fontFamily: "DMSansRegular",
      fontSize: 16,
      lineHeight: 18,
    },

    text16Medium: {
      fontSize: 16,
      fontFamily: "DMSansMedium",
      lineHeight: 18,
    },

    text25: {
      fontSize: 25,
      fontFamily: "DMSansSemiBold",
      lineHeight: 27,
    },

      text10Medium: {
        fontSize: 10,
        fontFamily: "DMSansMedium",
      },

    text12Medium: {
      fontSize: 12,
      fontFamily: "DMSansMedium",
      lineHeight: 14,
    },

    text11: {
      fontSize: 11,
      fontFamily: "DMSansRegular",
      lineHeight: 13,
    },

      text13: {
        fontSize: 13,
        fontFamily: "DMSansMedium",
        lineHeight: 15,
      },

      text14Regular: {
        fontSize: 14,
        fontFamily: "DMSansRegular",
      },
      text14Medium: {
        fontSize: 14,
        fontFamily: "DMSansMedium",
        lineHeight: 18,
      },
      text14SemiBold : {
        fontSize: 14,
        fontFamily: "DMSansSemiBold",
        lineHeight: 16,
      },

    text17: {
      fontSize: 17,
      fontFamily: "DMSansSemiBold",
      lineHeight: 19,
    },

    text17Medium: {
      fontSize: 17,
      fontFamily: "DMSansMedium",
      lineHeight: 19,
    },

    text18Medium: {
      fontSize: 18,
      fontFamily: "DMSansMedium",
      lineHeight: 20,
    },

    text24Bold: {
      fontSize: 24,
      fontFamily: "DMSansBold",
      lineHeight: 27,
    },

    text24Medium: {
      fontSize: 24,
      fontFamily: "DMSansMedium",
      lineHeight: 27,
    },


    text12Light: {
      fontSize: 12,
      fontFamily: "DMSansExtraLight",
      lineHeight: 14,
    },
  },
  borderRadii: {
    small: 8,
    default: 16,
    secondary: 20,
    medium: 32,
    rounded: 500
  },
  boxShadows: {
    primary: "0px 4px 20px 0px rgba(0, 0, 0, 0.15)",
    light: "0px 2px 8px rgba(0, 0, 0, 0.1)",
  },
});

export type Theme = typeof theme;
export type ThemeColors = keyof Theme["colors"];
export type Spacing = keyof Theme["spacing"];
export default theme;