import { IconName } from './Icon';

/**
 * Presets predefinidos para o GradientIconButton
 * Facilita o uso do componente com configurações comuns
 */

export type GradientIconButtonPreset = 
  | 'small'
  | 'medium' 
  | 'large'
  | 'action'
  | 'navigation';

export interface GradientIconButtonPresetConfig {
  size: number;
  iconSize: number;
  gradientColors: string[];
}

export const gradientIconButtonPresets: Record<GradientIconButtonPreset, GradientIconButtonPresetConfig> = {
  small: {
    size: 40,
    iconSize: 18,
    gradientColors: ['#535353', '#000000'],
  },
  medium: {
    size: 56,
    iconSize: 24,
    gradientColors: ['#535353', '#000000'],
  },
  large: {
    size: 72,
    iconSize: 30,
    gradientColors: ['#535353', '#000000'],
  },
  action: {
    size: 120,
    iconSize: 24,
    gradientColors: ['#535353', '#000000'],
  },
  navigation: {
    size: 56,
    iconSize: 22,
    gradientColors: ['#535353', '#000000'],
  },
};

/**
 * Mapeamento de tipos de ação para ícones
 * Facilita o uso do componente para ações específicas
 */
export type ActionType = 
  | 'transfer'
  | 'pix'
  | 'qrcode'
  | 'copy-paste'
  | 'wallet'
  | 'support'
  | 'home'
  | 'transactions'
  | 'card';

export const actionIconMap: Record<ActionType, IconName> = {
  transfer: 'receiveMoney',
  pix: 'pix',
  qrcode: 'qrCode',
  'copy-paste': 'copyPaste',
  wallet: 'wallet',
  support: 'support',
  home: 'home',
  transactions: 'moneyRecive',
  card: 'card',
};

/**
 * Labels padrão para cada tipo de ação
 */
export const actionLabelMap: Record<ActionType, string> = {
  transfer: 'Transferir',
  pix: 'PIX',
  qrcode: 'QR Code',
  'copy-paste': 'Copiar e Colar',
  wallet: 'Carteira',
  support: 'Suporte',
  home: 'Início',
  transactions: 'Transações',
  card: 'Cartão',
};

/**
 * Interface para uso simplificado do componente com presets
 */
export interface GradientIconButtonWithPresetProps {
  preset?: GradientIconButtonPreset;
  action?: ActionType;
  customIconName?: IconName;
  customTitle?: string;
  onPress: () => void;
  isLoading?: boolean;
  iconOnly?: boolean;
}
