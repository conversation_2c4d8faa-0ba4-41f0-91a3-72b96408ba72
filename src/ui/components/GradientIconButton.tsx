import { LinearGradient } from 'expo-linear-gradient';
import { ActivityIndicator, TouchableOpacity } from 'react-native';
import { useAppTheme } from '../theme/useAppTheme';
import { TouchableOpacityBoxProps } from './Box';
import { Icon, IconName } from './Icon';
import { Text, TextProps } from './Text';

export interface GradientIconButtonProps extends Omit<TouchableOpacityBoxProps, 'children'> {
  /** Nome do ícone a ser exibido */
  iconName: IconName;
  /** Texto do botão (opcional) */
  title?: string;
  /** Função chamada ao pressionar o botão */
  onPress: () => void;
  /** Tamanho do ícone */
  iconSize?: number;
  /** Tamanho do botão */
  size?: number;
  /** Cores do gradiente */
  gradientColors?: string[];
  /** Estado de carregamento */
  isLoading?: boolean;
  /** Props adicionais para o texto */
  textProps?: TextProps;
  /** Se deve mostrar apenas o ícone (sem texto) */
  iconOnly?: boolean;
}

export function GradientIconButton({
  iconName,
  title,
  onPress,
  iconSize = 24,
  size = 56,
  gradientColors = ['#535353', '#000000'],
  isLoading = false,
  textProps,
  iconOnly = false,
  activeOpacity = 0.8,
  ...touchableProps
}: GradientIconButtonProps) {
  const { colors } = useAppTheme();

  return (
    <TouchableOpacity
      onPress={onPress}
      activeOpacity={activeOpacity}
      disabled={isLoading}
      {...touchableProps}
    >
      <LinearGradient
        colors={gradientColors}
        start={[0, 0]}
        end={[1, 1]}
        style={{
          width: size,
          height: size,
          borderRadius: size / 2,
          justifyContent: 'center',
          alignItems: 'center',
          flexDirection: iconOnly ? 'column' : 'row',
          paddingHorizontal: iconOnly ? 0 : 12,
          gap: iconOnly ? 4 : 8,
        }}
      >
        {isLoading ? (
          <ActivityIndicator color="white" size="small" />
        ) : (
          <>
            <Icon 
              name={iconName} 
              color="white" 
              size={iconSize} 
            />
            {title && !iconOnly && (
              <Text 
                color="white" 
                variant="text12Medium"
                textAlign="center"
                flexShrink={1}
                {...textProps}
              >
                {title}
              </Text>
            )}
          </>
        )}
      </LinearGradient>
      
      {title && iconOnly && (
        <Text 
          color="text" 
          variant="text10Medium"
          textAlign="center"
          marginTop="s4"
          flexShrink={1}
          {...textProps}
        >
          {title}
        </Text>
      )}
    </TouchableOpacity>
  );
}
