import { Transaction } from "@/src/types/transaction";
import { formatCurrency } from "@/src/utils";
import { format } from "date-fns";
import { Box } from "./Box";
import { Icon } from "./Icon";
import { Text } from "./Text";

type CardWithdrawalProps = {
  data: Transaction;
}

export function CardWithdrawal({ data }: CardWithdrawalProps) {
  const amount = formatCurrency(data.amount)
  const dateWithdrawal = format(data.createdAt, "d MMM, HH:mm")
  
  return (
    <Box
      justifyContent="space-between"
      alignItems="center"
      borderRadius="default"
      flexDirection="row"
    >
      <Box flexDirection="row" alignItems="center" gap="s16">
        <Box
          alignItems="center"
          justifyContent="center"
          backgroundColor="gray50"
          borderRadius="rounded"
          padding="s16"
        >
          <Icon name="moneyRecive" color="green" size={24} />
        </Box>
        <Box gap="s8" flex={1}>
          <Text
            variant="text14Medium"
            color="textPrimary">Pagamento Recebido</Text>
          <Box justifyContent="space-between" flexDirection="row">
            <Text variant="text14SemiBold" color="textPrimary">
              {amount}
            </Text>
            <Text
              variant="text12Light"
              color="textTertiary">
                {dateWithdrawal}
            </Text>
          </Box>
        </Box>
      </Box>
    </Box>
  );
}