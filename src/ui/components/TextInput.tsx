import React, { forwardRef } from 'react';
import {
  TextInput as RNTextInput,
  TextInputProps as RNTextInputProps
} from 'react-native';

import { useAppTheme } from '../theme/useAppTheme';
import { Box, BoxProps } from './Box';
import { Text, TextProps } from './Text';

export interface TextInputProps extends RNTextInputProps {
  label: string;
  errorMessage?: string;
  rightComponent?: React.ReactElement;
  boxProps?: BoxProps;
  textProps?: TextProps;
}

export const TextInput = forwardRef<RNTextInput, TextInputProps>(({
  label,
  errorMessage,
  rightComponent,
  boxProps,
  textProps,
  ...rnTextInputProps
}, ref) => {
  const { colors } = useAppTheme();

  return (
    <Box>
      {label && (
        <Text mb="s10" variant="text14Medium" color='text' {...textProps} >
          {label}
        </Text>
      )}
      <Box
        {...textInputBoxStyle}
        backgroundColor="grayInput"
        borderWidth={0}
        borderRadius="small"
        height={56}
        {...boxProps}
      >
        <RNTextInput
          ref={ref}
          placeholderTextColor={colors.gray500}
          style={{
            color: colors.text,
            width: "100%",
          }}
          {...rnTextInputProps}
        />

        {rightComponent && (
          <Box ml="s16" justifyContent="center">
            {rightComponent}
          </Box>
        )}
      </Box>
      {errorMessage && (
        <Text variant="text13" color="text">
          {errorMessage}
        </Text>
      )}
    </Box>
  );
});

TextInput.displayName = 'TextInput';

const textInputBoxStyle: BoxProps = {
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: "s8",
  borderWidth: 2,
  borderRadius: "default",
  height: 50,
};