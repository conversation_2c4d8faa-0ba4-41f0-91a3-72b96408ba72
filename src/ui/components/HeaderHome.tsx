import LogoHeader from '@/src/assets/brand/logo-header.png';
import { Image, Platform } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Box } from "./Box";
import { IconButton } from "./IconButton";
import { Text } from "./Text";
import { useAuth } from '@/src/states';

export function HeaderHome() {
  const { top } = useSafeAreaInsets();
  const { signOut } = useAuth();

  return (
    <Box
      backgroundColor="background"
      style={{
        paddingTop: Platform.OS === "ios" ? 0 : top + 10,
        height: Platform.OS === "ios" ? top : 100,
      }}
      alignItems="center"
      justifyContent="space-between"
      flexDirection="row"
      paddingHorizontal="s24"
      paddingBottom="s12"
    >
      <Box flexDirection="row" borderRadius="rounded" alignItems="center" gap="s16">
        <Image source={LogoHeader} alt="Logo" />
        <Box gap="s4">
          <Text variant="text12Light" color="gray500">
            Ol<PERSON>,
          </Text>
          <Text variant="text17Medium" color="gray900">
            <PERSON><PERSON>
          </Text>
        </Box>
      </Box>
      <Box flexDirection='row' gap='s16'>
        <IconButton IconName="search" onPress={() => { }} />
        <IconButton IconName="signOut" onPress={signOut} />
      </Box>
    </Box>
  )
}