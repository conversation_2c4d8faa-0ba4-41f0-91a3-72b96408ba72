import React from 'react';

import { useNavigation } from '@react-navigation/native';

import { Platform } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Box, IconButton, Text, TouchableOpacityBox } from './index';

type Props = {
  title: string;
  canGoBack: boolean;
};
export function HeaderPrimary({ canGoBack, title }: Props) {
  const navigation = useNavigation();
  const { top } = useSafeAreaInsets();

  return (
    <Box
      backgroundColor="background"
      style={{
        paddingTop: Platform.OS === "ios" ? 0 : top + 10, 
        height: Platform.OS === "ios" ? top : 100,
      }}
      alignItems="center"
      justifyContent="space-between"
      flexDirection="row"
      paddingHorizontal="s24"
      paddingBottom="s12"
    >
      {canGoBack && (
        <TouchableOpacityBox
          onPress={navigation.goBack}
          flexDirection="row"
          alignItems="center"
          >
        <IconButton IconName="chevronLeft"  onPress={navigation.goBack} />
          {!title && (
            <Text variant="text14SemiBold" color="black" ml="s8">
              Voltar
            </Text>
          )} 
        </TouchableOpacityBox>
      )}
      {title && <Text variant="text18Medium" color="black">{title}</Text>}
      {title && <Box width={42} />}
    </Box>
  );
}