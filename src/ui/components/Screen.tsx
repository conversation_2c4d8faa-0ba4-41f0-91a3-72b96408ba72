import { PropsWithChildren } from "react";
import { Keyboard, KeyboardAvoidingView, Platform, SafeAreaView, ScrollView, TouchableWithoutFeedback } from "react-native";
import { useAppTheme } from "../theme/useAppTheme";
import { Box, BoxProps } from "./Box";

export function Screen({
  children,
  customHeader,
  scrollable = false,
  ...boxProps
}: PropsWithChildren & BoxProps & { scrollable?: boolean, customHeader?: React.ReactNode }) {
  const Container = scrollable ? ScrollView : Box;
  const { colors } = useAppTheme();
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background }}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : undefined}
      >
        {Boolean(customHeader) && customHeader}
        <Box
          flex={1}
          backgroundColor="background"
          paddingHorizontal="s24"
          {...boxProps}
        >
          <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <Container
              showsVerticalScrollIndicator={false}>{children}
            </Container>
          </TouchableWithoutFeedback>
        </Box>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
