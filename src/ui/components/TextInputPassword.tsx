import { useRef, useState } from "react";
import {
  Pressable,
  TextInput as RNTextInput,
  TextInputProps as RNTextInputProps,
  TouchableOpacity,
} from "react-native";
import { useAppTheme } from "../theme/useAppTheme";
import { Box, BoxProps } from "./Box";
import { Icon } from "./Icon";
import { Text, TextProps } from "./Text";

type TextInputProps = RNTextInputProps & {
  label: string;
  errorMessage?: string;
  boxProps?: BoxProps;
  textProps?: TextProps;
};
export function TextInputPassword({
  label,
  errorMessage,
  boxProps,
  textProps,
  ...textInputProps
}: TextInputProps) {
  const { colors, textVariants } = useAppTheme();
  const [isFocused, setIsFocused] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const inputRef = useRef<RNTextInput>(null);

  const borderColor = errorMessage
    ? "error"
    : isFocused
      ? "text"
      : "blueDark";

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  function focusInput() {
    inputRef.current?.focus();
  }

  return (
    <Box>
      {label && (
        <Text mb="s10" variant="text14Medium" {...textProps}>
          {label}
        </Text>
      )}
      <Pressable onPress={focusInput}>
        <Box
          {...textInputBoxStyle}
          backgroundColor="grayInput"
          borderWidth={0}
          borderRadius="small"
          height={56}
          {...boxProps}
        >
          <RNTextInput
            ref={inputRef}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            placeholderTextColor={colors.gray500}
            secureTextEntry={!isPasswordVisible}
            style={{
              color: colors.text,
              width: "100%",
            }}
            {...textInputProps}
          />

          <TouchableOpacity
            onPress={togglePasswordVisibility}
            style={{
              paddingHorizontal: 8,
              justifyContent: 'center',
              alignItems: 'center',
            }}
            activeOpacity={0.7}
          >
            {/* <Icon
              name={isPasswordVisible ? "eyeOff" : "eye"}
              size={24}
              color='text'
            /> */}
          </TouchableOpacity>
        </Box>
        {errorMessage && (
          <Text marginVertical="s4" variant="text12Light" color="text">
            {errorMessage}
          </Text>
        )}
      </Pressable>
    </Box>
  );
}

const textInputBoxStyle: BoxProps = {
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: "s8",
  paddingVertical: "s4",
  borderWidth: 2,
  borderRadius: "default",
  height: 50,
};