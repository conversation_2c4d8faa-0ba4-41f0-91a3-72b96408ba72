import {
  BackgroundColorProps,
  BorderProps,
  LayoutProps,
  ShadowProps,
  SpacingProps,
  SpacingShorthandProps
} from "@shopify/restyle";
import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { Theme } from "../theme/theme";

type RestyleTypes = BackgroundColorProps<Theme> &
  SpacingProps<Theme> &
  LayoutProps<Theme> &
  BorderProps<Theme> &
  ShadowProps<Theme> &
  SpacingShorthandProps<Theme>;

export interface GradientBoxProps extends Omit<RestyleTypes, 'backgroundColor'> {
  colors?: string[];
  children?: React.ReactNode;
}

export function GradientBox({
  colors = ['#020817', '#1E1E2D'],
  children,
  ...restProps
}: GradientBoxProps) {
  return (
    <LinearGradient
      colors={colors}
      start={[0, -1.9]}
      end={[1, 1]}
      style={{
        borderRadius: 16, // default border radius
        flex: 1,
      }}
      {...restProps}
    >
      {children}
    </LinearGradient>
  );
} 