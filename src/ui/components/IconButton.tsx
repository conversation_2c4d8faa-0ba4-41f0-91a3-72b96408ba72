import { Pressable, PressableProps } from "react-native"
import { Box } from "./Box"
import { Icon, IconName } from "./Icon"

type IconButtonProps = {
  IconName: IconName
  onPress: PressableProps["onPress"]
  color?: string
  size?: number
}

export function IconButton({ IconName, onPress, size = 42 }: IconButtonProps) {
  return (
    <Pressable onPress={onPress}>
      <Box backgroundColor="white"
      width={size}
      height={size}
      borderRadius="rounded"
      alignItems="center"
      justifyContent="center"
      >
        <Icon name={IconName} color="black" />
      </Box>
    </Pressable>
  )
}