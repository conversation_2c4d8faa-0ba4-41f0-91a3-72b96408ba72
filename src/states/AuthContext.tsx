import { SplashScreen, useRouter } from "expo-router";
import { deleteItemAsync, getItem, setItem } from "expo-secure-store";
import { createContext, ReactNode, useContext, useEffect, useState } from "react";
import { IS_LOGGED, TOKEN } from "../../constant";
import api from "../services/apiConfig";
import { Auth } from "../types/auth";
import { AuthResponse } from "../types/user";
type AuthState = {
  isLoggedIn: boolean;
  isReady: boolean;
  signIn: (auth: Auth) => Promise<void>;
  signOut: () => void;
  isLoading: boolean;
};

type AuthContextProviderProps = {
  children: ReactNode;
};

SplashScreen.preventAutoHideAsync();

export const AuthContext = createContext<AuthState>({} as AuthState);

export const AuthProvider = ({ children }: AuthContextProviderProps) => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isReady, setIsReady] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  async function signIn(auth: Auth) {
    try {
      setIsLoading(true);
      const { data, status } = await api.post<AuthResponse>("/login", auth);

      if (status === 200) {
        const { accessToken } = data;
        setItem(TOKEN, accessToken);
        setItem(IS_LOGGED, true.toString());
        setIsLoggedIn(true);
        router.replace("/(protected)/(tabs)");
      }
    } catch (error: any) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  }

  // async function signUp(auth: AuthRegister) {
  //   try {
  //     setIsLoading(true);
  //     const { status } = await api.post<AuthRegisterResponse>("/user/register", auth);

  //     if (status === 201) { 
  //       signIn({
  //         userEmail: auth.userEmail,
  //         userPassword: auth.userPassword,
  //       });
  //     }
  //   } catch (error) {
  //     console.error(error);
  //   } finally {
  //     setIsLoading(false);
  //   }
  // }

  async function signOut() {
    setIsLoggedIn(false);
    await deleteItemAsync(IS_LOGGED);
    await deleteItemAsync(TOKEN);
  }

  useEffect(() => {
    async function loadAuthState() {
      try {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        const authState = getItem(IS_LOGGED);
        if (authState) {
          setIsLoggedIn(authState === "true");
        }
      } catch (error) {
        console.error(error);
      } finally {
        setIsReady(true);
      }
    }
    loadAuthState();
  }, []);

  useEffect(() => {
    if (isReady) {
      SplashScreen.hideAsync();
    }
  }, [isReady]);


  return (
    <AuthContext.Provider value={{
      isLoggedIn,
      isReady,
      isLoading,
      signIn,
      signOut,
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export function useAuth() {
  return useContext(AuthContext);
}
