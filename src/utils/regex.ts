import { PixKeyType } from "../types/pixKeyType";

export const validatePixKey = (key: string): PixKeyType => {
  const numericKey = key.replace(/\D/g, '');

  // CPF: 11 dígitos
  if (/^\d{11}$/.test(numericKey)) return PixKeyType.CPF;

  // CNPJ: 14 dígitos
  if (/^\d{14}$/.test(numericKey)) return PixKeyType.CNPJ;

  // Email
  if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(key)) return PixKeyType.EMAIL;

  // Telefone no formato E.164: +55 ou 55 seguido de 10 ou 11 dígitos
  if (/^\+?55\d{10,11}$/.test(key.replace(/\D/g, ''))) return PixKeyType.PHONE;

  // Chave aleatória (UUID v4)
  if (/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(key)) {
    return PixKeyType.RANDOM;
  }

  return PixKeyType.UNKNOWN;
};
