import React from 'react';

import { IconBase } from '@/src/ui/components/Icon';
import { Path, Svg } from 'react-native-svg';

export function EditIcon({ size = 20, color = 'black' }: IconBase) {
  return (
    <Svg width={size} height={size} viewBox="0 0 19 20" fill="none">
      <Path d="M8.80709 2.56641H7.39976C3.88145 2.56641 2.47412 3.97373 2.47412 7.49205V11.714C2.47412 15.2323 3.88145 16.6397 7.39976 16.6397H11.6217C15.1401 16.6397 16.5474 15.2323 16.5474 11.714V10.3067" stroke={color} strokeWidth="1.26315" strokeLinecap="round" strokeLinejoin="round" />
      <Path d="M12.3535 3.28417L6.80866 8.82903C6.59756 9.04013 6.38646 9.45529 6.34424 9.75786L6.04167 11.8759C5.92908 12.6429 6.4709 13.1777 7.2379 13.0721L9.35592 12.7695C9.65146 12.7273 10.0666 12.5162 10.2848 12.3051L15.8296 6.76026C16.7866 5.80328 17.2369 4.69149 15.8296 3.28417C14.4223 1.87684 13.3105 2.32718 12.3535 3.28417Z" stroke={color} strokeWidth="1.26315" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
      <Path d="M11.5586 4.0791C11.7919 4.90764 12.2341 5.66239 12.8427 6.27104C13.4514 6.8797 14.2061 7.32187 15.0347 7.5552" stroke={color} strokeWidth="1.26315" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
    </Svg>
  );
}