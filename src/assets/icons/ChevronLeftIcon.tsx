import React from 'react';

import { IconBase } from '@/src/ui/components/Icon';
import { Path, Svg } from 'react-native-svg';

export function ChevronLeftIcon({ size = 20, color = 'black' }: IconBase) {
  return (
    <Svg width={size} height={size} viewBox="0 0 20 20" fill="none">
      <Path d="M13 4L7 10L13 16" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    </Svg>
  );
}