import React from 'react';

import { IconBase } from '@/src/ui/components/Icon';
import { ClipPath, Defs, G, Path, Rect, Svg } from 'react-native-svg';

export function WalletCheckIcon({ size = 20, color = 'black' }: IconBase) {
  return (
    <Svg width={size} height={size} viewBox="0 0 25 24" fill="none" >
      <G clipPath="url(#clip0_628_1944)">
        <Path d="M3.65234 19L4.64234 19.99L6.77234 18.02M17.9623 7.04996C17.7223 7.00996 17.4723 6.99996 17.2123 6.99996H7.21234C6.93234 6.99996 6.66234 7.01996 6.40234 7.05996C6.54234 6.77996 6.74234 6.51996 6.98234 6.27996L10.2323 3.01996C10.8924 2.36654 11.7836 2 12.7123 2C13.6411 2 14.5323 2.36654 15.1923 3.01996L16.9423 4.78996C17.5823 5.41996 17.9223 6.21996 17.9623 7.04996Z" stroke={color} strokeLinecap="round" strokeLinejoin="round" />
        <Path d="M22.2109 12V17C22.2109 20 20.2109 22 17.2109 22H7.84094C8.15094 21.74 8.42094 21.42 8.63094 21.06C9.00094 20.46 9.21094 19.75 9.21094 19C9.21094 16.79 7.42094 15 5.21094 15C4.01094 15 2.94094 15.53 2.21094 16.36V12C2.21094 9.28 3.85094 7.38 6.40094 7.06C6.66094 7.02 6.93094 7 7.21094 7H17.2109C17.4709 7 17.7209 7.01 17.9609 7.05C20.5409 7.35 22.2109 9.26 22.2109 12Z" stroke={color} strokeLinecap="round" strokeLinejoin="round" />
        <Path d="M22.2109 12.5H19.2109C18.1109 12.5 17.2109 13.4 17.2109 14.5C17.2109 15.6 18.1109 16.5 19.2109 16.5H22.2109" stroke={color} strokeLinecap="round" strokeLinejoin="round" />
        <Path d="M9.21095 19C9.21095 19.75 9.00095 20.46 8.63095 21.06C8.27841 21.6525 7.77766 22.143 7.17798 22.4831C6.57831 22.8233 5.90038 23.0014 5.21095 23C4.52151 23.0014 3.84358 22.8233 3.24391 22.4831C2.64423 22.143 2.14348 21.6525 1.79095 21.06C1.41029 20.4404 1.20947 19.7272 1.21095 19C1.21095 16.79 3.00095 15 5.21095 15C7.42095 15 9.21095 16.79 9.21095 19Z" stroke={color} strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
      </G>
      <Defs>
        <ClipPath id="clip0_628_1944">
          <Rect width={24} height={24} fill="white" transform="translate(0.210938)" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}