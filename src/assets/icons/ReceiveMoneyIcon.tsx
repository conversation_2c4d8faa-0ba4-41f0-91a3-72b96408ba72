import React from 'react';

import { IconBase } from '@/src/ui/components/Icon';
import { Path, Svg } from 'react-native-svg';

export function ReceiveMoneyIcon({ size = 20, color = 'black' }: IconBase) {
  return (
    <Svg width={size} height={size} viewBox="0 0 29 28" fill="none">
      <Path d="M11.583 16.0417C11.583 17.1733 12.458 18.0833 13.5313 18.0833H15.7247C16.658 18.0833 17.4163 17.29 17.4163 16.2983C17.4163 15.2367 16.9497 14.8517 16.2613 14.6067L12.7497 13.3817C12.0613 13.1367 11.5947 12.7633 11.5947 11.69C11.5947 10.71 12.353 9.905 13.2863 9.905H15.4797C16.553 9.905 17.428 10.815 17.428 11.9467M14.4997 8.75V19.25" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <Path d="M26.1663 13.9997C26.1663 20.4397 20.9397 25.6663 14.4997 25.6663C8.05967 25.6663 2.83301 20.4397 2.83301 13.9997C2.83301 7.55967 8.05967 2.33301 14.4997 2.33301M26.1663 6.99967V2.33301M26.1663 2.33301H21.4997M26.1663 2.33301L20.333 8.16634" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    </Svg>
  );
}