import React from 'react';

import { IconBase } from '@/src/ui/components/Icon';
import { Path, Svg } from 'react-native-svg';

export function MoneyReciveIcon({ size = 20, color = 'black' }: IconBase) {
  return (
    <Svg width={size} height={size} viewBox="0 0 30 31" fill="none">
      <Path d="M11.875 17.6211C11.875 18.8336 12.8125 19.8086 13.9625 19.8086H16.3125C17.3125 19.8086 18.125 18.9586 18.125 17.8961C18.125 16.7586 17.625 16.3461 16.8875 16.0836L13.125 14.7711C12.3875 14.5086 11.8875 14.1086 11.8875 12.9586C11.8875 11.9086 12.7 11.0461 13.7 11.0461H16.05C17.2 11.0461 18.1375 12.0211 18.1375 13.2336M15 9.80859V21.0586" stroke={color} strokeLinecap="round" strokeLinejoin="round" />
      <Path d="M27.5 15.4336C27.5 22.3336 21.9 27.9336 15 27.9336C8.1 27.9336 2.5 22.3336 2.5 15.4336C2.5 8.53359 8.1 2.93359 15 2.93359" stroke={color} strokeWidth="2" strokeLinecap="round" stroke-Linejoin="round" />
      <Path d="M21.25 4.18359V9.18359M21.25 9.18359H26.25M21.25 9.18359L27.5 2.93359" stroke={color} strokeLinecap="round" strokeLinejoin="round" />
    </Svg>
  );
}