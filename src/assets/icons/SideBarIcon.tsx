import React from 'react';

import { IconBase } from '@/src/ui/components/Icon';
import { Path, Svg } from 'react-native-svg';

export function SideBarIcon({ size = 20, color = 'black' }: IconBase) {
  return (
    <Svg width={size} height={size} viewBox="0 0 28 22" fill="none">
      <Path d="M2 2.43359H25.8777" stroke={color} strokeWidth="3" strokeLinecap="round" />
      <Path d="M2 11.4336H25.8777" stroke={color} strokeWidth="3" strokeLinecap="round" />
      <Path d="M2 20.4336H25.8777" stroke={color} strokeWidth="3" strokeLinecap="round" />
    </Svg>
  );
}