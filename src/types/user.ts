interface Seller {
  id: string;
  legalName: string;
  tradeName: string;
  cnpj: string;
  email: string;
  telephone: string;
  userId: string;
  status: string;
  documentVerifiedBy: string;
  documentVerifiedAt: string;
  documentVerified: boolean;
  documentStatus: string;
  rejectionReason: string | null;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  frontDocumentKey: string;
  backDocumentKey: string;
  cnpjKey: string;
  selfieKey: string;
  socialContractKey: string;
  bankStatementKey: string;
  createdAt: string;
  balance: number;
}

interface User {
  id: string;
  name: string;
  telephone: string;
  email: string;
  role: string;
  activated: boolean;
  is2faEnabled: boolean;
  createdAt: string;
  seller: Seller;
  affiliate: null;
}

export interface AuthResponse {
  accessToken: string;
  user: User;
}