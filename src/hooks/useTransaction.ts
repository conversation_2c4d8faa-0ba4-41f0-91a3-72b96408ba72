import { useInfiniteQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import api from "../services/apiConfig";
import { PaginationResponse } from "../types/pagination";
import { ItemTransaction, Transaction } from "../types/transaction";

export function useTransaction() {
  const getTransactions = async ({ pageParam = 0 }): Promise<PaginationResponse<Transaction>> => {
    const response = await api.get<PaginationResponse<Transaction>>(
      `/transactions?skip=${pageParam}&take=20`
    );  
    return response.data;
  }
  
  const {
    data,
    isLoading,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage
  } = useInfiniteQuery({
    queryKey: ['transactions'],
    queryFn: getTransactions,
    getNextPageParam: (lastPage) => {
      const nextSkip = lastPage.skip + lastPage.take;
      return nextSkip < lastPage.count ? nextSkip : undefined;
    },
    initialPageParam: 0,
  });

  // Achata todas as páginas em uma única lista
  const transactions = useMemo(() => {
    if (!data?.pages) return { items: [], count: 0 };
    
    const allItems = data.pages.flatMap(page => page.data);
    const totalCount = data.pages[0]?.count ?? 0;
    
    return {
      items: allItems,
      count: totalCount
    };
  }, [data?.pages]);

  return { 
    transactions, 
    transactionsItems: transactions.items[0]?.items ?? [],
    isLoading, 
    error, 
    fetchNextPage, 
    hasNextPage, 
    isFetchingNextPage 
  };
}