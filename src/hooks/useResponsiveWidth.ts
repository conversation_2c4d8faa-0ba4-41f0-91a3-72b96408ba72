import { useEffect, useState } from "react";
import { Dimensions } from "react-native";

interface UseResponsiveWidthProps {
  itemCount: number;
  horizontalPadding?: number;
  gapSize?: number;
}

export function useResponsiveWidth({ 
  itemCount, 
  horizontalPadding = 40, 
  gapSize = 16 
}: UseResponsiveWidthProps) {
  const [width, setWidth] = useState(100);

  const calculateWidth = () => {
    const screenWidth = Dimensions.get('window').width;
    const totalGaps = gapSize * (itemCount - 1);
    const availableWidth = screenWidth - horizontalPadding - totalGaps;
    
    return Math.floor(availableWidth / itemCount);
  };

  useEffect(() => {
    // Calcula a largura inicial
    setWidth(calculateWidth());

    // Listener para mudanças nas dimensões da tela
    const subscription = Dimensions.addEventListener('change', () => {
      setWidth(calculateWidth());
    });

    return () => subscription?.remove();
  }, [itemCount, horizontalPadding, gapSize]);

  return width;
} 