import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import api from "../services/apiConfig";
import { Balance, BalanceResponse } from "../types/balance";
import { Report, ReportResponse } from "../types/report";
import { Withdrawal } from "../types/withdrawal";
import { PaginationResponse } from "../types/pagination";

interface WithdrawalRequest {
  amount: number;
  pixKey: string;
  pixKeyType: string; // CPF - PHONE - EMAIL - EVP
  method: string;
  metadata?: {
    sellerExternalRef: string;
  }
}

interface WithdrawalResponse {
  status: boolean;
  data: {
        id: string;
        amount: number;
        method: string;
        pixKey: string;
        pixKeyType: string;
        status: string;
        withdrawalType: string;
        approvedAt: string;
        processedAt: string;
        createdAt: string;
        externalRef: string;
        end2end: string;
        metadata: string;
    }
}

const BALANCE_PATH = 'seller-wallet/balance';
const REPORT_PATH = 'sellers/me/report';
const WITHDRAWALS_LIST_PATH = 'withdrawals';
const WITHDRAWAL_PATH = 'withdrawals';

const getBalance = async (): Promise<Balance> => {
  const response = await api.get<BalanceResponse>(BALANCE_PATH);  
  return response.data.data;
}

const getReport = async (): Promise<Report> => {
  const response = await api.get<ReportResponse>(REPORT_PATH);
  return response.data.data;
}

const getWithdrawal = async (): Promise<Withdrawal[]> => {
  const response = await api.get<PaginationResponse<Withdrawal>>(WITHDRAWALS_LIST_PATH);
  return response.data.data;
}

export function useWalletData() {
  const queryClient = useQueryClient();

  const withdrawalMutation = useMutation({
    mutationFn: async (data: WithdrawalRequest) => {
      const response = await api.post<WithdrawalResponse>(WITHDRAWAL_PATH, data);
      return response.data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["balance"] });
      queryClient.invalidateQueries({ queryKey: ["withdrawal"] });
    },
    onError: (error) => {
      console.log("Erro ao processar saque:", error);
    },
  });

  const { 
    data: balance, 
    isLoading: isLoadingBalance, 
    error: balanceError, 
    refetch: refetchBalance 
  } = useQuery({
    queryKey: ['balance'],
    queryFn: getBalance,
    retry: false,
  });

  const { 
    data: report, 
    isLoading: isLoadingReport, 
    error: reportError, 
    refetch: refetchReport 
  } = useQuery({
    queryKey: ['report'],
    queryFn: getReport,
  });

  const { 
    data: withdrawal, 
    isLoading: isLoadingWithdrawal, 
    error: withdrawalError, 
    refetch: refetchWithdrawal 
  } = useQuery({
    queryKey: ['withdrawal'],
    queryFn: getWithdrawal,
  });

  const isLoading = isLoadingBalance || isLoadingReport || isLoadingWithdrawal;
  const error = balanceError || reportError || withdrawalError;

  const refetch = () => {
    refetchBalance();
    refetchReport();
    refetchWithdrawal();
  };

  return { 
    balance,
    createWithdrawal: withdrawalMutation.mutate,
    report,
    withdrawal,
    isLoading,
    error,
    refetch 
  };
}