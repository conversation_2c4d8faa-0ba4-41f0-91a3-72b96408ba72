{"expo": {"name": "WiteTec", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "appwitetec", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "app-wite-tec", "buildNumber": "1", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "android": {"versionCode": 1, "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#3390FF"}, "edgeToEdgeEnabled": true, "package": "com.app.wite.tec"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#3390FF"}], "expo-secure-store", "expo-web-browser"], "experiments": {"typedRoutes": true}, "extra": {"router": {}}, "owner": "wite2025", "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/6792527f-57c0-423a-91b4-a9f4ead381fb"}}}