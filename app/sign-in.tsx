import WBrandWhite from "@/src/assets/brand/w-brand-light.svg";
import WBrand from "@/src/assets/brand/w-brand.svg";
import { useAuth } from "@/src/states/AuthContext";
import { Box, Button, Text, TextInput, TextInputPassword } from "@/src/ui/components";
import { LinearGradient } from "expo-linear-gradient";
import { useState } from "react";
import {
  Dimensions,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TouchableWithoutFeedback,
  View
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function SignInScreen() {
  const { signIn, isLoading } = useAuth();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  // const router = useRouter();

  function handleSignIn() {
    signIn({
      email: email,
      password: password,
    });
  }

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <LinearGradient
        colors={['#000000', '#1D1D1D']}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        style={{
          width: '100%',
          height: '100%',
        }}
      >
        {/* W Brand Background Pattern */}
        <View style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
          <WBrand
            height={Dimensions.get('window').height}
          />
        </View>

        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={{ flex: 1 }}
        >
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{
              flexGrow: 1,
              justifyContent: 'center',
              alignItems: 'center',
            }}
            keyboardShouldPersistTaps="handled"
          >
            <SafeAreaView style={{
              paddingHorizontal: 16,
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1,
            }}>
              <Box alignItems="center">
                <WBrandWhite
                  width={60}
                  height={60}
                  fill="white"
                  color="white"
                />
              </Box>
              <Box backgroundColor="white" width="95%" mt="s20" padding="s20" borderRadius="default">
                <Box alignItems="center" my="s20">
                  <Text variant="text24Medium" color="text" alignSelf="center">
                    Acesse a sua conta
                  </Text>
                </Box>

                <Box gap="s24" mb="s24">
                  <TextInput
                    label="E-mail"
                    autoCapitalize="none"
                    value={email}
                    onChangeText={setEmail}
                    placeholder="Digite seu email"
                    keyboardType="email-address"
                  />
                  <TextInputPassword
                    label="Senha"
                    autoCapitalize="none"
                    value={password}
                    onChangeText={setPassword}
                    placeholder="Digite sua senha"
                  />
                </Box>

                <Box mb="s8" gap="s16">
                  <Button
                    boxProps={{
                      padding: "s20",
                    }}
                    variant="dark"
                    title="Entrar"
                    onPress={handleSignIn}
                    isLoading={isLoading}
                    disabled={!email || !password}
                  />
                </Box>
              </Box>
            </SafeAreaView>
          </ScrollView>
        </KeyboardAvoidingView>
      </LinearGradient>
    </TouchableWithoutFeedback>
  );
}