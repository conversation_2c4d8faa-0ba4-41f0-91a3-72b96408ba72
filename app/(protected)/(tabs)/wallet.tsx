import { useWalletData } from "@/src/hooks";
import { Box, CardBalance, Icon, Screen, Text } from "@/src/ui/components";
import { HeaderPrimary } from "@/src/ui/components/HeaderPrimary";
import { formatCurrency } from "@/src/utils";

export default function WalletScreen() {
  const { balance, withdrawal, createWithdrawal, report } = useWalletData();

  return (
    <Screen
      scrollable
      customHeader={
        <HeaderPrimary
          title="Carteira"
          canGoBack={true}
        />}
    >
      <Box gap="s24">
        <CardBalance
          boxProps={{ marginTop: 's20' }}
          label="Saldo disponível"
          value={formatCurrency(balance?.balance ?? 0)}
        />
        <CardBalance
          preset="outline"
          icon="pix"
          sizeIcon={24}
          label="PIX (Hoje)"
          value={formatCurrency(report?.pixHoje ?? 0)}
          boxProps={{ flex: 1 }}
        />
        <CardBalance
          preset="outline"
          icon="walletCheck"
          sizeIcon={24}
          label="Cartão (Hoje)"
          value={formatCurrency(report?.cartaoHoje ?? 0)}
          boxProps={{ flex: 1 }}
        />
        <CardBalance
          preset="outline"
          icon="barCode"
          sizeIcon={24}
          label="Boleto (Hoje)"
          value={formatCurrency(report?.boletoHoje ?? 0)}
          boxProps={{ flex: 1 }}
        />
        <Box py="s20" pb="s32" px="s24" backgroundColor="white" borderRadius="secondary">
          <CardBalance
            label="Saldo a liberar"
            value={formatCurrency(report?.pendingAmount ?? 0)}
          />
          <Box flexDirection="row" mt="s32" alignItems="center" justifyContent="space-between" gap="s8">
            <Box flexDirection="row" alignItems="center" gap="s8">
              <Icon name="pix" size={32} color="gray900" />
              <Text fontSize={18} color="gray900" fontFamily="DMSansRegular">
                {report?.salesByMethod.PIX ?? 0}
              </Text>
            </Box>

            <Box flexDirection="row" alignItems="center" gap="s8">
              <Icon name="walletCheck" size={32} color="gray900" />
              <Text fontSize={18} color="gray900" fontFamily="DMSansRegular">
                {report?.salesByMethod.CREDIT_CARD ?? 0}
              </Text>
            </Box>

            <Box flexDirection="row" alignItems="center" gap="s8">
              <Icon name="barCode" size={32} color="gray900" />
              <Text fontSize={18} color="gray900" fontFamily="DMSansRegular">
                {report?.salesByMethod.BOLETO ?? 0}
              </Text>
            </Box>
          </Box>
        </Box>
      </Box>
    </Screen>
  );
}