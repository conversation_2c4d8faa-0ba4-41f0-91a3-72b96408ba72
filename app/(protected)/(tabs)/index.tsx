import { dataWithdrawal } from "@/mock";
import { useResponsiveWidth, useWalletData } from "@/src/hooks";
import { useTransaction } from "@/src/hooks/useTransaction";
import { Box, CardBalance, CardTransaction, EmptyState, HeaderHome, NavigationOption, OptionNavigate } from "@/src/ui/components";
import { Text } from "@/src/ui/components/Text";
import { formatCurrency } from "@/src/utils/format-current";
import { useRouter } from "expo-router";
import { FlatList, SafeAreaView } from "react-native";

export default function HomeScreen() {
  const { report, balance } = useWalletData();
  const router = useRouter();
  const {
    transactionsItems,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage
  } = useTransaction();

  const options: NavigationOption[] = [
    { type: "pix" },
    { type: "wallet" },
    { type: "transactions" },
    { type: "support" },
  ];

  // Calcula a largura responsiva
  const optionWidth = useResponsiveWidth({
    itemCount: options.length,
    horizontalPadding: 40,
    gapSize: 16
  });

  const renderHeader = () => (
    <Box>
      <CardBalance
        boxProps={{ 
          marginTop: 's20',
          marginHorizontal: 's20',
        }}
        label="Disponível"
        value={formatCurrency(balance?.balance ?? 0)}
      />

      <Box gap="s16" marginTop="s24" flexDirection="row" paddingHorizontal="s20">
        {options.map((option, index) => (
          <OptionNavigate key={index} width={optionWidth} type={option.type} />
        ))}
      </Box>

      <Box gap="s8" marginTop="s24" flexDirection="row" paddingHorizontal="s20">
        <CardBalance
          preset="outline"
          icon="pix"
          sizeIcon={16}
          sizeWrapperIcon={40}
          label="PIX (Hoje)"
          value={formatCurrency(report?.salesByMethod.PIX ?? 0)}
          boxProps={{ flex: 1 }}
        />
        <CardBalance
          preset="outline"
          icon="walletCheck"
          sizeIcon={16}
          sizeWrapperIcon={40}
          label="Cartão (Hoje)"
          value={formatCurrency(report?.salesByMethod.CREDIT_CARD ?? 0)}
          boxProps={{ flex: 1 }}
        />
      </Box>

      <Box
        marginTop="s24"
        marginHorizontal="s20"
        backgroundColor="white"
        borderTopLeftRadius="secondary"
        borderTopRightRadius="secondary"
        paddingHorizontal="s16"
        paddingTop="s24"
        paddingBottom="s8"
      >
        <Box
          flexDirection="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Text variant="text18Medium">Últimas transações</Text>
          {transactionsItems?.length > 0 && (
            <Text onPress={() => router.push('/transaction-history')} color="blue" variant="text14Medium">Ver todas</Text>
          )}
        </Box>
      </Box>
    </Box>
  );

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <Box flex={1}>
        <HeaderHome />
        <FlatList
          data={transactionsItems ?? []}
          keyExtractor={(item) => item.id}
          contentContainerStyle={{
            flexGrow: 1,
          }}
          showsVerticalScrollIndicator={false}
          onEndReached={() => {
            if (hasNextPage && !isFetchingNextPage) {
              fetchNextPage();
            }
          }}
          onEndReachedThreshold={0.3}
          ListHeaderComponent={renderHeader}
          ListEmptyComponent={() => (
            <Box
              marginHorizontal="s20"
              backgroundColor="white"
              borderBottomLeftRadius="secondary"
              borderBottomRightRadius="secondary"
              padding="s16"
            >
              <EmptyState
                icon="moneyRecive"
                title="Nenhuma transação ainda"
                description="Suas transações aparecerão aqui conforme seus clientes realizarem compras"
              />
            </Box>
          )}
          ListFooterComponent={() => {
            if (isFetchingNextPage) {
              return (
                <Box alignItems="center" paddingVertical="s16" marginHorizontal="s20">
                  <Text variant="text14Regular" color="textTertiary">
                    Carregando mais transações...
                  </Text>
                </Box>
              );
            }
            return <Box height={20} />;
          }}
          renderItem={({ item }) => (
            <Box
              marginHorizontal="s20"
              backgroundColor="white"
              paddingHorizontal="s16"
              paddingVertical="s8"
            >
              <CardTransaction data={item} />
            </Box>
          )}
        />
      </Box>
    </SafeAreaView>
  );
}