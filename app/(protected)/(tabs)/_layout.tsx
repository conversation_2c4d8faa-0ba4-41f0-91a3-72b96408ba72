import { Icon } from "@/src/ui/components/Icon";
import { useAppTheme } from "@/src/ui/theme/useAppTheme";
import { Tabs, usePathname, useRouter } from "expo-router";
import React, { useRef } from "react";
import { Animated, Platform, Text, TouchableOpacity, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

function AnimatedTabButton({ tab, isActive, onPress }: any) {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const { colors } = useAppTheme();

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const handlePress = () => {
    // Animação de "bounce" no clique
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.9,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
      }),
    ]).start();
    
    onPress();
  };

  return (
    <TouchableOpacity
      onPress={handlePress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={0.7}
      style={{
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 8,
        minWidth: 60,
      }}
    >
      <Animated.View
        style={{
          transform: [{ scale: scaleAnim }],
          alignItems: 'center',
        }}
      >
        <Icon
          name={tab.icon as any}
          color={isActive ? 'black' : 'borderGrayTabBar'}
        />
        <Text style={{
          fontSize: 12,
          fontFamily: "DMSansRegular",
          color: isActive ? colors.black : colors.borderGrayTabBar,
          marginTop: 4,
          textAlign: 'center',
        }}>
          {tab.title}
        </Text>
      </Animated.View>
    </TouchableOpacity>
  );
}

function CustomTabBar() {
  const { colors } = useAppTheme();
  const router = useRouter();
  const pathname = usePathname();
  const insets = useSafeAreaInsets();

  const tabs = [
    { name: "index", title: "Início", icon: "home", route: "/(protected)/(tabs)/" },
    { name: "wallet", title: "Carteira", icon: "wallet", route: "/(protected)/(tabs)/wallet" },
    { name: "transaction-history", title: "Transações", icon: "moneyRecive", route: "/(protected)/(tabs)/transaction-history" }
  ];

  const isActive = (tabName: string) => {
    if (tabName === "index") {
      return pathname === "/" || pathname === "/(protected)/(tabs)/" || pathname === "/(protected)/(tabs)";
    }
    return pathname.includes(tabName);
  };

  const handleTabPress = (route: string) => {
    router.navigate(route as any);
  };

  return (
    <View style={{
      backgroundColor: colors.grayTabBar,
      paddingBottom: Platform.OS === "ios" ? insets.bottom : 10,
      paddingTop: 10,
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      gap: 40,
    }}>
      {tabs.map((tab) => (
        <AnimatedTabButton
          key={tab.name}
          tab={tab}
          isActive={isActive(tab.name)}
          onPress={() => handleTabPress(tab.route)}
        />
      ))}
    </View>
  );
}

export default function TabLayout() {
  return (
    <Tabs
      tabBar={() => <CustomTabBar />}
      screenOptions={{
        headerShown: false,
      }}
    >
    </Tabs>
  );
}