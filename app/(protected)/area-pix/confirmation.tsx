import { Box, Button, Icon, Screen, Text, TouchableOpacityBox } from "@/src/ui/components";
import { HeaderPrimary } from "@/src/ui/components/HeaderPrimary";
import { useLocalSearchParams, useRouter } from "expo-router";

// Função para identificar o tipo da chave PIX
const getPixKeyType = (pixKey: string): "CPF" | "CNPJ" | "PHONE" | "EMAIL" | "EVP" => {
  if (!pixKey) return "EVP";

  // Email - verifica primeiro pois pode conter pontos
  if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(pixKey)) {
    return "EMAIL";
  }

  // Remove toda formatação para análise de números
  const numbersOnly = pixKey.replace(/\D/g, '');

  // Telefone (10 ou 11 dígitos) - verifica se tem formato de telefone
  if (/^\(\d{2}\)\s?\d{4,5}-?\d{4}$/.test(pixKey) || /^\d{10,11}$/.test(numbersOnly)) {
    return "PHONE";
  }

  // CPF (11 dígitos) - verifica se tem formato de CPF
  if (/^\d{3}\.?\d{3}\.?\d{3}-?\d{2}$/.test(pixKey) || (numbersOnly.length === 11 && !/^\(\d{2}\)/.test(pixKey))) {
    return "CPF";
  }

  // CNPJ (14 dígitos) - verifica se tem formato de CNPJ
  if (/^\d{2}\.?\d{3}\.?\d{3}\/?\d{4}-?\d{2}$/.test(pixKey) || numbersOnly.length === 14) {
    return "CNPJ";
  }

  // Chave aleatória (UUID)
  if (/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i.test(pixKey)) {
    return "EVP";
  }

  // Default para chave aleatória
  return "EVP";
};

// Função para converter valor para centavos
const convertToCents = (value: string): number => {
  if (!value) return 0;

  // Remove "R$" e espaços
  let cleanValue = value.replace(/[R$\s]/g, '');

  // Se tem vírgula, ela é o separador decimal
  if (cleanValue.includes(',')) {
    // Remove pontos (separadores de milhares) e mantém apenas a vírgula
    cleanValue = cleanValue.replace(/\./g, '');

    const parts = cleanValue.split(',');
    if (parts.length === 2) {
      // Garante que a parte decimal tenha 2 dígitos
      const decimal = parts[1].padEnd(2, '0').substring(0, 2);
      return parseInt(parts[0] + decimal) || 0;
    }
  }

  // Se não tem vírgula, remove pontos e assume valor inteiro em reais
  cleanValue = cleanValue.replace(/\./g, '');
  const numValue = parseInt(cleanValue) || 0;

  // Se o valor é muito pequeno (menos de 3 dígitos), assume que está em reais
  if (numValue < 100) {
    return numValue * 100;
  }

  // Caso contrário, assume que já está em centavos
  return numValue;
};

export default function ConfirmationScreen() {
  const router = useRouter();
  const { transferValue, pixKey } = useLocalSearchParams();
  return (
    <Screen
      customHeader={
        <HeaderPrimary
          title=" "
          canGoBack
        />}
      flex={1}
    >
      <Box paddingTop="s16" height="100%">
        <Box flex={1}>
          <Text
            fontSize={25}
            fontFamily="DMSansMedium"
            color="zinc"
            lineHeight={25}
          >
            Transferindo
          </Text>
          <Box flexDirection="row" justifyContent="space-between" alignItems="center" mt="s24">
            <Text
              fontSize={24}
              lineHeight={26}
              fontFamily="DMSansBold"
              color="zinc50"
            >
              {transferValue}
            </Text>
            <TouchableOpacityBox
              flexDirection="row"
              alignItems="center"
              gap="s4"
              onPress={() => router.push({
                pathname: '/(protected)/area-pix/send-pix',
                params: {
                  transferValue: transferValue,
                  pixKey: pixKey,
                  isEdit: 'true',
                },
              })}>
              <Icon name="edit" size={19} color="zinc50" />
              <Text fontSize={16} fontFamily="DMSansRegular" color="zinc50">Alterar</Text>
            </TouchableOpacityBox>
          </Box>

          <Text fontSize={16} fontFamily="DMSansRegular" color="zinc50" mt="s24">
            para Nicolas da Silva
          </Text>

          {/* Informações da transferência */}
          <Box mt="s32" gap="s16">
            <Box flexDirection="row" justifyContent="space-between">
              <Text fontSize={16} fontFamily="DMSansMedium" color="zinc50">CPF</Text>
              <Text fontSize={16} fontFamily="DMSansRegular" color="zinc50">23.456.789-0</Text>
            </Box>
            <Box flexDirection="row" justifyContent="space-between">
              <Text fontSize={16} fontFamily="DMSansMedium" color="zinc50">Instituição</Text>
              <Text fontSize={16} fontFamily="DMSansRegular" color="zinc50">99PAY IP S.A</Text>
            </Box>
            <Box flexDirection="row" justifyContent="space-between">
              <Text fontSize={16} fontFamily="DMSansMedium" color="zinc50">Agência</Text>
              <Text fontSize={16} fontFamily="DMSansRegular" color="zinc50">1</Text>
            </Box>
            <Box flexDirection="row" justifyContent="space-between">
              <Text fontSize={16} fontFamily="DMSansMedium" color="zinc50">Conta</Text>
              <Text fontSize={16} fontFamily="DMSansRegular" color="zinc50">1</Text>
            </Box>
          </Box>
        </Box>

        {/* Botão fixo na parte inferior */}
        <Box paddingBottom="s40" paddingTop="s16">
          <Button
            variant="gradient"
            title="Confirmar transferência"
            onPress={() => {
              const transferData = {
                amount: convertToCents(transferValue as string),
                pixKey: pixKey as string,
                pixKeyType: getPixKeyType(pixKey as string),
                method: "PIX"
              };

              console.log(transferData);
            }}
            boxProps={{
              borderRadius: 'rounded',
            }}
          />
        </Box>
      </Box>
    </Screen>
  );
} 