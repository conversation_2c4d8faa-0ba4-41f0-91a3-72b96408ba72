import { Box, Button, Icon, Screen, Text, TouchableOpacityBox } from "@/src/ui/components";
import { HeaderPrimary } from "@/src/ui/components/HeaderPrimary";
import { useLocalSearchParams, useRouter } from "expo-router";

export default function ConfirmationScreen() {
  const router = useRouter();
  const { transferValue } = useLocalSearchParams();
  return (
    <Screen
      customHeader={
        <HeaderPrimary
          title=" "
          canGoBack
        />}
      flex={1}
    >
      <Box paddingTop="s16" height="100%">
        <Box flex={1}>
          <Text
            fontSize={25}
            fontFamily="DMSansMedium"
            color="zinc"
            lineHeight={25}
          >
            Transferindo
          </Text>
          <Box flexDirection="row" justifyContent="space-between" alignItems="center" mt="s24">
            <Text
              fontSize={24}
              lineHeight={26}
              fontFamily="DMSansBold"
              color="zinc50"
            >
              {transferValue}
            </Text>
            <TouchableOpacityBox
              flexDirection="row"
              alignItems="center"
              gap="s4"
              onPress={() => router.push({
                pathname: '/(protected)/area-pix/send-pix',
                params: {
                  transferValue: transferValue,
                  isEdit: 'true',
                },
              })}>
              <Icon name="edit" size={19} color="zinc50" />
              <Text fontSize={16} fontFamily="DMSansRegular" color="zinc50">Alterar</Text>
            </TouchableOpacityBox>
          </Box>

          <Text fontSize={16} fontFamily="DMSansRegular" color="zinc50" mt="s24">
            para Nicolas da Silva
          </Text>

          {/* Informações da transferência */}
          <Box mt="s32" gap="s16">
            <Box flexDirection="row" justifyContent="space-between">
              <Text fontSize={16} fontFamily="DMSansMedium" color="zinc50">CPF</Text>
              <Text fontSize={16} fontFamily="DMSansRegular" color="zinc50">23.456.789-0</Text>
            </Box>
            <Box flexDirection="row" justifyContent="space-between">
              <Text fontSize={16} fontFamily="DMSansMedium" color="zinc50">Instituição</Text>
              <Text fontSize={16} fontFamily="DMSansRegular" color="zinc50">99PAY IP S.A</Text>
            </Box>
            <Box flexDirection="row" justifyContent="space-between">
              <Text fontSize={16} fontFamily="DMSansMedium" color="zinc50">Agência</Text>
              <Text fontSize={16} fontFamily="DMSansRegular" color="zinc50">1</Text>
            </Box>
            <Box flexDirection="row" justifyContent="space-between">
              <Text fontSize={16} fontFamily="DMSansMedium" color="zinc50">Conta</Text>
              <Text fontSize={16} fontFamily="DMSansRegular" color="zinc50">1</Text>
            </Box>
          </Box>
        </Box>

        {/* Botão fixo na parte inferior */}
        <Box paddingBottom="s40" paddingTop="s16">
          <Button
            variant="gradient"
            title="Confirmar transferência"
            onPress={() => console.log('Revisar transferência')}
            boxProps={{
              borderRadius: 'rounded',
            }}
          />
        </Box>
      </Box>
    </Screen>
  );
} 