import { useWalletData } from "@/src/hooks/useWalletData";
import { Box, Button, Icon, Screen, Text, TouchableOpacityBox } from "@/src/ui/components";
import { HeaderPrimary } from "@/src/ui/components/HeaderPrimary";
import { formatCurrency } from "@/src/utils/format-current";
import { useLocalSearchParams, useRouter } from "expo-router";

// Função para identificar o tipo da chave PIX
const getPixKeyType = (pixKey: string): "CPF" | "CNPJ" | "PHONE" | "EMAIL" | "EVP" | "COPY_PASTE" | "EDIT" => {
  if (!pixKey) return "EVP";

  // Email - verifica primeiro pois pode conter pontos
  if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(pixKey)) {
    return "EMAIL";
  }

  // Remove toda formatação para análise de números
  const numbersOnly = pixKey.replace(/\D/g, '');

  // Telefone - verifica se tem formato específico de telefone com DDD
  if (/^\(\d{2}\)\s?\d{4,5}-?\d{4}$/.test(pixKey)) {
    return "PHONE";
  }

  // CNPJ (14 dígitos) - verifica primeiro para evitar confusão
  if (/^\d{2}\.?\d{3}\.?\d{3}\/?\d{4}-?\d{2}$/.test(pixKey) || numbersOnly.length === 14) {
    return "CNPJ";
  }

  // CPF (11 dígitos) - verifica se tem formato de CPF ou 11 dígitos que não são telefone
  if (/^\d{3}\.?\d{3}\.?\d{3}-?\d{2}$/.test(pixKey)) {
    return "CPF";
  }

  // Para 11 dígitos sem formatação, verifica se é telefone ou CPF
  if (numbersOnly.length === 11) {
    // Se começa com padrão de telefone (DDD + 9 + 8 dígitos), é telefone
    if (/^[1-9][1-9]9[0-9]{8}$/.test(numbersOnly)) {
      return "PHONE";
    }
    // Caso contrário, é CPF
    return "CPF";
  }

  // Telefone (10 dígitos) - telefone fixo
  if (numbersOnly.length === 10 && /^[1-9][1-9][2-5][0-9]{7}$/.test(numbersOnly)) {
    return "PHONE";
  }

  // Chave aleatória (UUID)
  if (/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i.test(pixKey)) {
    return "EVP";
  }

  // Default para chave aleatória
  return "EVP";
};

// Função para extrair additionalData do código PIX
const extractPixAdditionalData = (pixCode: string): string => {
  try {
    let position = 0;

    while (position < pixCode.length - 4) {
      const id = pixCode.substring(position, position + 2);
      const lengthStr = pixCode.substring(position + 2, position + 4);
      const length = parseInt(lengthStr, 10);

      if (isNaN(length) || length < 0) break;

      const value = pixCode.substring(position + 4, position + 4 + length);

      // Campo 62 contém os dados adicionais
      if (id === '62' && value) {
        return value;
      }

      position += 4 + length;
    }

    return ''; // AdditionalData não encontrado
  } catch (error) {
    return '';
  }
};

// Função para extrair nome do recebedor do código PIX
const extractPixReceiverName = (pixCode: string): string => {
  try {
    let position = 0;

    while (position < pixCode.length - 4) {
      const id = pixCode.substring(position, position + 2);
      const lengthStr = pixCode.substring(position + 2, position + 4);
      const length = parseInt(lengthStr, 10);

      if (isNaN(length) || length < 0) break;

      const value = pixCode.substring(position + 4, position + 4 + length);

      // Campo 59 contém o nome do comerciante/recebedor
      if (id === '59' && value) {
        return value;
      }

      position += 4 + length;
    }

    return ''; // Nome não encontrado
  } catch (error) {
    return '';
  }
};

// Função para extrair valor do código PIX em centavos
const extractPixAmount = (pixCode: string): number => {
  try {
    let position = 0;

    while (position < pixCode.length - 4) {
      const id = pixCode.substring(position, position + 2);
      const lengthStr = pixCode.substring(position + 2, position + 4);
      const length = parseInt(lengthStr, 10);

      if (isNaN(length) || length < 0) break;

      const value = pixCode.substring(position + 4, position + 4 + length);

      // Campo 54 contém o valor
      if (id === '54' && value) {
        const amount = parseFloat(value);
        return Math.round(amount * 100); // Converte para centavos
      }

      position += 4 + length;
    }

    return 0; // Valor não especificado
  } catch (error) {
    return 0;
  }
};

// Função para extrair informações do código PIX (EVP)
const extractPixInfo = (pixCode: string) => {
  try {
    const info: any = {
      originalCode: pixCode,
      length: pixCode.length,
      segments: {},
      parsed: {}
    };

    let position = 0;

    // Parse do código PIX seguindo o padrão EMV
    while (position < pixCode.length - 4) {
      const id = pixCode.substring(position, position + 2);
      const lengthStr = pixCode.substring(position + 2, position + 4);
      const length = parseInt(lengthStr, 10);

      if (isNaN(length) || length < 0) break;

      const value = pixCode.substring(position + 4, position + 4 + length);

      // Mapeia os campos conhecidos
      switch(id) {
        case '00':
          info.parsed.version = value;
          break;
        case '01':
          info.parsed.initiation = value;
          break;
        case '26':
          info.parsed.merchantAccount = value;
          // Tenta extrair informações do campo 26 (conta do comerciante)
          if (value.includes('br.gov.bcb.pix')) {
            info.parsed.pixType = 'Chave PIX';
          }
          break;
        case '52':
          info.parsed.merchantCategory = value;
          break;
        case '53':
          info.parsed.currency = value === '986' ? 'BRL (Real Brasileiro)' : value;
          break;
        case '54':
          info.parsed.amount = value ? `R$ ${parseFloat(value).toFixed(2)}` : 'Não especificado';
          break;
        case '58':
          info.parsed.country = value === 'BR' ? 'Brasil' : value;
          break;
        case '59':
          info.parsed.merchantName = value;
          break;
        case '60':
          info.parsed.merchantCity = value;
          break;
        case '62':
          info.parsed.additionalData = value;
          break;
        case '63':
          info.parsed.crc = value;
          break;
        default:
          info.segments[id] = value;
      }

      position += 4 + length;
    }

    return info;
  } catch (error) {
    return {
      originalCode: pixCode,
      error: 'Erro ao processar código PIX',
      details: error
    };
  }
};

// Função para converter valor para centavos
const convertToCents = (value: string): number => {
  if (!value) return 0;

  // Remove "R$" e espaços
  let cleanValue = value.replace(/[R$\s]/g, '');

  // Se tem vírgula, ela é o separador decimal
  if (cleanValue.includes(',')) {
    // Remove pontos (separadores de milhares) e mantém apenas a vírgula
    cleanValue = cleanValue.replace(/\./g, '');

    const parts = cleanValue.split(',');
    if (parts.length === 2) {
      // Garante que a parte decimal tenha 2 dígitos
      const decimal = parts[1].padEnd(2, '0').substring(0, 2);
      return parseInt(parts[0] + decimal) || 0;
    }
  }

  // Se não tem vírgula, remove pontos e assume valor inteiro em reais
  cleanValue = cleanValue.replace(/\./g, '');
  const numValue = parseInt(cleanValue) || 0;

  // Se o valor é muito pequeno (menos de 3 dígitos), assume que está em reais
  if (numValue < 100) {
    return numValue * 100;
  }

  // Caso contrário, assume que já está em centavos
  return numValue;
};

export default function ConfirmationScreen() {
  const router = useRouter();
  const { createWithdrawal } = useWalletData();
  const { transferValue, pixKey, pixKeyType } = useLocalSearchParams();

  // Se for EVP (COPY_PASTE), extrai o valor, nome e additionalData do código PIX
  const isEVP = pixKeyType === 'COPY_PASTE';
  const displayValue = isEVP && pixKey
    ? formatCurrency(extractPixAmount(pixKey as string))
    : transferValue;
  const receiverName = isEVP && pixKey
    ? extractPixReceiverName(pixKey as string)
    : '';
  const additionalData = isEVP && pixKey
    ? extractPixAdditionalData(pixKey as string)
    : '';

  // Para o identificador, mostra os últimos 10 caracteres do additionalData se for EVP
  const displayIdentifier = isEVP && additionalData
    ? additionalData.slice(-10)
    : pixKey;

  // Validações para o botão de confirmar
  const finalAmount = isEVP && pixKey
    ? extractPixAmount(pixKey as string)
    : convertToCents(transferValue as string);

  const MINIMUM_AMOUNT = 500; // R$ 5,00 em centavos
  const isAmountTooLow = finalAmount < MINIMUM_AMOUNT;
  const canConfirmTransfer = !isAmountTooLow;
  return (
    <Screen
      customHeader={
        <HeaderPrimary
          title=" "
          canGoBack
        />}
      flex={1}
    >
      <Box paddingTop="s16" height="100%">
        <Box flex={1}>
          <Text
            fontSize={25}
            fontFamily="DMSansMedium"
            color="zinc"
            lineHeight={25}
          >
            Transferindo
          </Text>
          <Box flexDirection="row" justifyContent="space-between" alignItems="center" mt="s24">
            <Text
              fontSize={24}
              lineHeight={26}
              fontFamily="DMSansBold"
              color="zinc50"
            >
              {displayValue}
            </Text>
            {!isEVP && (
              <TouchableOpacityBox
                flexDirection="row"
                alignItems="center"
                gap="s4"
                onPress={() => {
                  // Para EVP (COPY_PASTE), vai para tela de copia e cola
                  if (pixKeyType === 'COPY_PASTE') {
                    router.push({
                      pathname: '/(protected)/area-pix/copy-paste-pix',
                      params: {
                        pixCode: pixKey,
                        isEdit: 'true',
                      },
                    });
                  } else {
                    // Para PIX normal, vai direto para o step de valor (amount)
                    router.push({
                      pathname: '/(protected)/area-pix/send-pix',
                      params: {
                        step: 'amount',
                        transferValue: transferValue,
                        pixKey: pixKey,
                        isEdit: 'true',
                      },
                    });
                  }
                }}>
                <Icon name="edit" size={19} color="zinc50" />
                <Text fontSize={16} fontFamily="DMSansRegular" color="zinc50">Alterar</Text>
              </TouchableOpacityBox>
            )}
          </Box>

          {isEVP && receiverName && (
            <Text fontSize={16} fontFamily="DMSansRegular" color="zinc50" mt="s24">
              para {receiverName}
            </Text>
          )}

          {/* Informações da transferência */}
          <Box mt="s32" gap="s16">
            <Box flexDirection="row" justifyContent="space-between">
              <Text fontSize={16} fontFamily="DMSansMedium" color="zinc50">
                {isEVP && additionalData ? 'Identificador' : 'Chave PIX'}
              </Text>
              <Text fontSize={16} fontFamily="DMSansRegular" color="zinc50">
                {isEVP && additionalData ? displayIdentifier : pixKey}
              </Text>
            </Box>
            {/* <Box flexDirection="row" justifyContent="space-between">
              <Text fontSize={16} fontFamily="DMSansMedium" color="zinc50">Instituição</Text>
              <Text fontSize={16} fontFamily="DMSansRegular" color="zinc50">99PAY IP S.A</Text>
            </Box>
            <Box flexDirection="row" justifyContent="space-between">
              <Text fontSize={16} fontFamily="DMSansMedium" color="zinc50">Agência</Text>
              <Text fontSize={16} fontFamily="DMSansRegular" color="zinc50">1</Text>
            </Box>
            <Box flexDirection="row" justifyContent="space-between">
              <Text fontSize={16} fontFamily="DMSansMedium" color="zinc50">Conta</Text>
              <Text fontSize={16} fontFamily="DMSansRegular" color="zinc50">1</Text>
            </Box> */}
          </Box>
        </Box>

        {/* Mensagem de erro */}
        {isAmountTooLow && (
          <Box paddingBottom="s16" paddingTop="s16" alignItems="center">
            <Text fontSize={14} fontFamily="DMSansRegular" style={{ color: '#FF0000' }} textAlign="center">
              Valor mínimo para saque é de R$ 5,00
            </Text>
          </Box>
        )}

        {/* Botão fixo na parte inferior */}
        <Box paddingBottom="s40" paddingTop="s16">
          {canConfirmTransfer ? (
            <Button
              variant="gradient"
              title="Confirmar transferência"
              onPress={async () => {
              try {
                // Se for EVP, usa o valor extraído do código PIX
                const finalAmount = isEVP && pixKey
                  ? extractPixAmount(pixKey as string)
                  : convertToCents(transferValue as string);

                const transferData = {
                  amount: finalAmount,
                  pixKey: pixKey as string,
                  pixKeyType: pixKeyType === 'COPY_PASTE' ? 'COPY_PASTE' :
                             pixKeyType === 'EDIT' ? getPixKeyType(pixKey as string) :
                             getPixKeyType(pixKey as string),
                  method: "PIX"
                };

                console.log('=== DADOS DA TRANSFERÊNCIA ===');
                console.log(transferData);

                // Se for código PIX (COPY_PASTE), extrair informações do EVP
                if (pixKeyType === 'COPY_PASTE') {
                  console.log('=== INFORMAÇÕES DO CÓDIGO PIX (EVP) ===');
                  const pixInfo = extractPixInfo(pixKey as string);
                  console.log(pixInfo);
                }

                // Remove formatação apenas para CPF, CNPJ e telefone
                const pixKeyType = transferData.pixKeyType;
                const cleanPixKey = (pixKeyType === 'CPF' || pixKeyType === 'CNPJ' || pixKeyType === 'PHONE')
                  ? (pixKey as string).replace(/[.,\-\s()]/g, '')
                  : (pixKey as string);

                // Chama a função createWithdrawal para enviar o PIX
                const result = await createWithdrawal({
                  amount: finalAmount,
                  pixKey: cleanPixKey,
                  pixKeyType: transferData.pixKeyType,
                  method: "PIX"
                });

                console.log('=== RESULTADO DA TRANSFERÊNCIA ===');
                console.log(result);

                // Se sucesso, navega de volta para a tela inicial
                if (result !== null && result !== undefined) {
                  // Aqui você pode adicionar navegação para tela de sucesso
                  router.push('/(protected)/(tabs)');
                }

              } catch (error) {
                console.error('Erro ao processar transferência:', error);
                // Aqui você pode mostrar um toast ou modal de erro
              }
            }}
            boxProps={{
              borderRadius: 'rounded',
            }}
          />
          ) : (
            <Button
              variant="light"
              title="Confirmar transferência"
              onPress={() => {}} // Botão desabilitado, não faz nada
              disabled={true}
              boxProps={{
                borderRadius: 'rounded',
                opacity: 0.5,
              }}
            />
          )}
        </Box>
      </Box>
    </Screen>
  );
} 