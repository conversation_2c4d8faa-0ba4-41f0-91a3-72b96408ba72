import { Box, Button, Screen, TextInput } from "@/src/ui/components";
import { HeaderPrimary } from "@/src/ui/components/HeaderPrimary";
import { Text } from "@/src/ui/components/Text";
import { zodResolver } from "@hookform/resolvers/zod";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Controller, useForm } from "react-hook-form";
import { Clipboard } from 'react-native';
import { z } from "zod";

// Schema de validação para código PIX
const pixCodeSchema = z.object({
  pixCode: z.string()
    .min(1, "Código PIX é obrigatório")
    .min(10, "Código PIX deve ter pelo menos 10 caracteres")
    .refine(
      (val) => {
        // Validação básica para código PIX Copia e Cola
        // Geralmente começa com números e contém caracteres específicos
        return val.length >= 10 && val.trim().length > 0;
      },
      { message: "Digite um código PIX válido" }
    ),
});

type PixCodeFormData = z.infer<typeof pixCodeSchema>;

export default function CopyPastePixScreen() {
  const router = useRouter();
  const { pixCode, isEdit } = useLocalSearchParams<{
    pixCode?: string;
    isEdit?: string;
  }>();

  const IS_EDIT_MODE = isEdit === 'true';

  // Form para código PIX
  const pixCodeForm = useForm<PixCodeFormData>({
    resolver: zodResolver(pixCodeSchema),
    defaultValues: {
      pixCode: IS_EDIT_MODE && pixCode ? pixCode : '',
    },
    mode: 'onChange',
  });

  const handlePixCodeSubmit = (data: PixCodeFormData) => {
    // Navegar diretamente para a tela de confirmação
    router.push({
      pathname: '/(protected)/area-pix/confirmation',
      params: {
        transferValue: "R$ 0,00", // Valor será extraído do código PIX
        pixKey: data.pixCode, // Usando o código PIX como chave
        pixKeyType: 'COPY_PASTE' // Tipo especial para copia e cola
      }
    });
  };

  const handlePasteFromClipboard = async () => {
    try {
      const clipboardContent = await Clipboard.getString();
      if (clipboardContent) {
        pixCodeForm.setValue('pixCode', clipboardContent);
      }
    } catch (error) {
      console.error('Erro ao colar do clipboard:', error);
    }
  };

  return (
    <Screen
      customHeader={
        <HeaderPrimary
          title=" "
          canGoBack
        />}
    >
      <Box height="100%" paddingTop="s16">
        <Text fontSize={25} fontWeight={'500'} fontFamily="DMSansMedium" lineHeight={27} mt="s14">
          {`Informe o código do Pix${'\n'}Copia e Cola`}
        </Text>

        {/* Input de código PIX */}
        <Box mt="s32">
          <Box
            borderBottomWidth={1}
            borderBottomColor="border"
            flexDirection="row"
          >
            <Box flex={1}>
              <Controller
                control={pixCodeForm.control}
                name="pixCode"
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    label=""
                    placeholderTextColor="#3D40457F"
                    placeholder="Cole aqui o código PIX"
                    value={value}
                    onChangeText={onChange}
                    multiline
                    numberOfLines={4}
                    style={{
                      fontSize: 16,
                      borderWidth: 0,
                      paddingHorizontal: 0,
                      backgroundColor: 'transparent',
                      textAlignVertical: 'top',
                    }}
                    boxProps={{
                      backgroundColor: 'background',
                    }}
                  />
                )}
              />
            </Box>
          </Box>

          {/* Botão para colar */}
          <Box mt="s16">
            <Button
              variant="light"
              title="Colar código"
              onPress={handlePasteFromClipboard}
              boxProps={{
                borderRadius: 'rounded',
              }}
            />
          </Box>

          {/* Erro de validação */}
          {pixCodeForm.formState.errors.pixCode && (
            <Text fontSize={14} mt="s8" style={{ color: '#FF0000' }}>
              {pixCodeForm.formState.errors.pixCode.message}
            </Text>
          )}
        </Box>

        {pixCodeForm.watch('pixCode') && pixCodeForm.formState.isValid && (
          <Box alignItems="center" mt="s56">
            <Button
              variant="gradient"
              title="Continuar"
              onPress={pixCodeForm.handleSubmit(handlePixCodeSubmit)}
              boxProps={{
                width: '70%',
                borderRadius: 'rounded',
              }}
            />
          </Box>
        )}

        <Box style={{
          flex: 1,
        }}>
        </Box>
      </Box>
    </Screen>
  );
}
