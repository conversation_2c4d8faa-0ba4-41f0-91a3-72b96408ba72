import { useWalletData } from "@/src/hooks/useWalletData";
import { Box, Button, GradientIconButton, Screen, TextInput } from "@/src/ui/components";
import { HeaderPrimary } from "@/src/ui/components/HeaderPrimary";
import { Text } from "@/src/ui/components/Text";
import { formatCurrency } from "@/src/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Controller, useForm } from "react-hook-form";
import { Clipboard, Keyboard, Platform } from "react-native";
import { z } from "zod";

// Schema de validação por step
const amountSchema = z.object({
  amount: z.string()
    .min(1, "Valor é obrigatório")
    .refine(
      (val) => {
        const numValue = parseFloat(val.replace(',', '.'));
        return !isNaN(numValue) && numValue > 0;
      },
      { message: "Digite um valor válido maior que zero" }
    )
    .refine(
      (val) => {
        const numValue = parseFloat(val.replace(',', '.'));
        return numValue <= 100000; // limite de R$ 100.000
      },
      { message: "Valor excede o limite permitido" }
    ),
});

const pixCodeSchema = z.object({
  pixCode: z.string()
    .min(1, "Código PIX é obrigatório")
    .min(10, "Código PIX deve ter pelo menos 10 caracteres")
    .refine(
      (val) => {
        // Validação básica para código PIX Copia e Cola
        // Geralmente começa com números e contém caracteres específicos
        return val.length >= 10 && val.trim().length > 0;
      },
      { message: "Digite um código PIX válido" }
    ),
});

type AmountFormData = z.infer<typeof amountSchema>;
type PixCodeFormData = z.infer<typeof pixCodeSchema>;

export default function CopyPastePixScreen() {
  const router = useRouter();
  const { balance } = useWalletData();
  const { step, transferValue, pixCode, isEdit } = useLocalSearchParams<{
    step?: 'amount' | 'pixcode';
    transferValue?: string;
    pixCode?: string;
    isEdit?: string;
  }>();

  const IS_EDIT_MODE = isEdit === 'true';
  const currentStep = step || 'amount';
  const isAmountStep = currentStep === 'amount';
  const isPixCodeStep = currentStep === 'pixcode';

  // Form para step de valor
  const amountForm = useForm<AmountFormData>({
    resolver: zodResolver(amountSchema),
    defaultValues: {
      amount: IS_EDIT_MODE ? transferValue : '0,00',
    },
    mode: 'onChange',
  });

  // Form para step de código PIX
  const pixCodeForm = useForm<PixCodeFormData>({
    resolver: zodResolver(pixCodeSchema),
    defaultValues: {
      pixCode: IS_EDIT_MODE && pixCode ? pixCode : '',
    },
    mode: 'onChange',
  });

  const handleAmountSubmit = (data: AmountFormData) => {
    router.push({
      pathname: '/(protected)/area-pix/copy-paste-pix',
      params: {
        step: 'pixcode',
        transferValue: `${formatCurrency(Number(data.amount))}`
      }
    });
  };

  const handlePixCodeSubmit = (data: PixCodeFormData) => {
    // Navegar diretamente para a tela de confirmação
    router.push({
      pathname: '/(protected)/area-pix/confirmation',
      params: {
        transferValue: transferValue,
        pixKey: data.pixCode, // Usando o código PIX como chave
        pixKeyType: 'COPY_PASTE' // Tipo especial para copia e cola
      }
    });
  };

  const handlePasteFromClipboard = async () => {
    try {
      const clipboardContent = await Clipboard.getString();
      if (clipboardContent) {
        pixCodeForm.setValue('pixCode', clipboardContent);
      }
    } catch (error) {
      console.error('Erro ao colar do clipboard:', error);
    }
  };

  const handleSubmit = isAmountStep
    ? amountForm.handleSubmit(handleAmountSubmit)
    : pixCodeForm.handleSubmit(handlePixCodeSubmit);

  const isFormValid = isAmountStep
    ? amountForm.formState.isValid
    : pixCodeForm.formState.isValid;

  return (
    <Screen
      customHeader={
        <HeaderPrimary
          title=" "
          canGoBack
        />}
    >
      <Box height="100%" paddingTop="s16">
        {isAmountStep ? (
          <Text fontSize={25} fontWeight={'500'} fontFamily="DMSansMedium" lineHeight={27} mt="s14">
            {`Qual é o valor da${'\n'}transferência?`}
          </Text>
        ) : (
          <Box mt="s14">
            <Text fontSize={25} fontWeight={'500'} fontFamily="DMSansMedium" lineHeight={27}>
              Cole o código PIX
            </Text>
            <Text fontSize={25} fontWeight={'500'} fontFamily="DMSansMedium" lineHeight={27}>
              para transferir <Text fontWeight={'700'} fontSize={22} fontFamily="DMSansBold">{transferValue}</Text>
            </Text>
          </Box>
        )}

        {isAmountStep && (
          <Box gap="s8" mt="s12">
            <Text fontSize={17} fontFamily="DMSansRegular" color="zinc50">
              Saldo disponível de
            </Text>
            <Text fontFamily="DMSansBold" color="gray900">
              {formatCurrency(balance?.balance ?? 0)}
            </Text>
          </Box>
        )}

        {/* primeira etapa - valor */}
        {isAmountStep && (
          <Box mt="s32">
            <Box
              borderBottomWidth={1}
              borderBottomColor="border"
            >
              <Box flexDirection="row">
                <Box flex={1}>
                  <Controller
                    control={amountForm.control}
                    name="amount"
                    render={({ field: { onChange, value } }) => (
                      <TextInput
                        label=""
                        placeholder="0,00"
                        value={formatCurrency(Number(value.replace(/[^0-9]/g, '')))}
                        onChangeText={(text) => onChange(text.replace(/[^0-9]/g, ''))}
                        keyboardType="numeric"
                        style={{
                          fontSize: 25,
                          fontWeight: '600',
                          fontFamily: 'DMSansSemiBold',
                          color: '#1F1F1F',
                          paddingHorizontal: 0,
                          flex: 1,
                        }}
                        boxProps={{
                          backgroundColor: 'background',
                        }}
                      />
                    )}
                  />
                </Box>
              </Box>
            </Box>

            {/* Erro de validação */}
            {amountForm.formState.errors.amount && (
              <Text fontSize={14} mt="s8" style={{ color: '#FF0000' }}>
                {amountForm.formState.errors.amount.message}
              </Text>
            )}
          </Box>
        )}

        {/* Input de código PIX - segunda etapa */}
        {isPixCodeStep && (
          <Box mt="s32">
            <Box
              borderBottomWidth={1}
              borderBottomColor="border"
              flexDirection="row"
            >
              <Box flex={1}>
                <Controller
                  control={pixCodeForm.control}
                  name="pixCode"
                  render={({ field: { onChange, value } }) => (
                    <TextInput
                      label=""
                      placeholderTextColor="#3D40457F"
                      placeholder="Cole aqui o código PIX"
                      value={value}
                      onChangeText={onChange}
                      multiline
                      numberOfLines={4}
                      style={{
                        fontSize: 16,
                        borderWidth: 0,
                        paddingHorizontal: 0,
                        backgroundColor: 'transparent',
                        textAlignVertical: 'top',
                      }}
                      boxProps={{
                        backgroundColor: 'background',
                      }}
                    />
                  )}
                />
              </Box>
            </Box>

            {/* Botão para colar */}
            <Box mt="s16">
              <Button
                variant="light"
                title="Colar código"
                onPress={handlePasteFromClipboard}
                boxProps={{
                  borderRadius: 'rounded',
                }}
              />
            </Box>

            {/* Erro de validação */}
            {pixCodeForm.formState.errors.pixCode && (
              <Text fontSize={14} mt="s8" style={{ color: '#FF0000' }}>
                {pixCodeForm.formState.errors.pixCode.message}
              </Text>
            )}
          </Box>
        )}

        {isPixCodeStep && (
          <Box alignItems="center" mt="s56">
            <Button
              variant="gradient"
              title="Continuar"
              onPress={pixCodeForm.handleSubmit(handlePixCodeSubmit)}
              boxProps={{
                width: '70%',
                borderRadius: 'rounded',
              }}
            />
          </Box>
        )}

        <Box style={{
          flex: 1,
        }}>
        </Box>

        {!isPixCodeStep && (
          <GradientIconButton
            iconName="arrowRight"
            onPress={() => {
              Keyboard.dismiss();
              handleSubmit();
            }}
            iconOnly
            disabled={!isFormValid}
            style={{
              position: 'absolute',
              bottom: Platform.OS === 'ios' ? 16 : 65,
              right: 20,
              opacity: isFormValid ? 1 : 0.5,
            }}
          />
        )}
      </Box>
    </Screen>
  );
}
