import { AuthProvider } from "@/src/states/AuthContext";
import { PortalProvider } from "@gorhom/portal";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import { StatusBar } from "expo-status-bar";
import React from "react";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import "react-native-reanimated";
import { ThemeContext } from "@shopify/restyle";
import theme from "@/src/ui/theme/theme";

const queryClient = new QueryClient();

function AppNavigator() {

  return (
    <>
      <Stack screenOptions={{
        headerShown: false,
        contentStyle: { backgroundColor: "#FFFFFF" },
        animation: 'slide_from_right',
        animationDuration: 400,
      }}>
        <Stack.Screen name="(protected)" options={{ headerShown: false, }} />
        <Stack.Screen
          name="sign-in"
          options={{
            contentStyle: { backgroundColor: "#FFFFFF" },
            animation: 'slide_from_left',
            animationDuration: 400,
          }}
        />
      </Stack>
      <StatusBar style="light" />
    </>
  );
}

export default function RootLayout() {
  const [loaded] = useFonts({
    DMSansBlack: require("../assets/fonts/DMSans-Black.ttf"),
    DMSansBlackItalic: require("../assets/fonts/DMSans-BlackItalic.ttf"),
    DMSansBold: require("../assets/fonts/DMSans-Bold.ttf"),
    DMSansBoldItalic: require("../assets/fonts/DMSans-BoldItalic.ttf"),
    DMSansExtraBold: require("../assets/fonts/DMSans-ExtraBold.ttf"),
    DMSansExtraBoldItalic: require("../assets/fonts/DMSans-ExtraBoldItalic.ttf"),
    DMSansExtraLight: require("../assets/fonts/DMSans-ExtraLight.ttf"),
    DMSansExtraLightItalic: require("../assets/fonts/DMSans-ExtraLightItalic.ttf"),
    DMSansItalic: require("../assets/fonts/DMSans-Italic.ttf"),
    DMSansLight: require("../assets/fonts/DMSans-Light.ttf"),
    DMSansLightItalic: require("../assets/fonts/DMSans-LightItalic.ttf"),
    DMSansMedium: require("../assets/fonts/DMSans-Medium.ttf"),
    DMSansMediumItalic: require("../assets/fonts/DMSans-MediumItalic.ttf"),
    DMSansRegular: require("../assets/fonts/DMSans-Regular.ttf"),
    DMSansSemiBold: require("../assets/fonts/DMSans-SemiBold.ttf"),
    DMSansSemiBoldItalic: require("../assets/fonts/DMSans-SemiBoldItalic.ttf"),
    DMSansThin: require("../assets/fonts/DMSans-Thin.ttf"),
    DMSansThinItalic: require("../assets/fonts/DMSans-ThinItalic.ttf"),
  });

  if (!loaded) {
    return null;
  }

  if (__DEV__) {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    require("../src/libs/ReactotronConfig");
  }

  return (
    <ThemeContext.Provider value={theme}>
      <QueryClientProvider client={queryClient}>
        <GestureHandlerRootView style={{ flex: 1 }}>
          <AuthProvider>
            <PortalProvider>
              <AppNavigator />
              <StatusBar style="dark" />
            </PortalProvider>
          </AuthProvider>
        </GestureHandlerRootView>
      </QueryClientProvider>
    </ThemeContext.Provider>
  );
}